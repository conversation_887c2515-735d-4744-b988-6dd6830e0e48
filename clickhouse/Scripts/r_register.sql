-- auto-generated definition
create table r_register_kafka
(
    Ktable         String,
    ProductID      UInt8,
    ChannelType    UInt32,
    Platform       UInt8,
    AppLanguage    UInt8,
    Country        String,
    AccType        UInt8,
    AppVersion     String,
    NowDate        Date,
    TimeStampValue DateTime,
    PlayerId       UInt64,
    LoginType      UInt8,
    DeviceModel    String,
    DeviceBrand    String,
    Os             String,
    OsLanguage     String,
    Resolution     String,
    AdjustId       String,
    Idfa           String,
    TimeZone       String,
    DeviceName     String,
    DeviceCode     String,
    Mvno           String,
    Android        String,
    ThirdToken     String,
    BundleName     String,
    Name           String,
    Avatar         UInt32,
    AvatarUrl      String,
    RegisterTime   UInt64
)
    engine = Kafka SETTINGS kafka_broker_list = '************:9092', kafka_topic_list = 'r_register', kafka_group_name = 'ck', kafka_format = 'CSV', format_csv_delimiter = '|', kafka_skip_broken_messages = 10;

-- auto-generated definition
create table r_register_ods
(
    Ktable         String,
    ProductID      UInt8,
    ChannelType    UInt32,
    Platform       UInt8,
    AppLanguage    UInt8,
    Country        String,
    AccType        UInt8,
    AppVersion     String,
    NowDate        Date,
    TimeStampValue DateTime,
    PlayerId       UInt64,
    LoginType      UInt8,
    DeviceModel    String,
    DeviceBrand    String,
    Os             String,
    OsLanguage     String,
    Resolution     String,
    AdjustId       String,
    Idfa           String,
    TimeZone       String,
    DeviceName     String,
    DeviceCode     String,
    Mvno           String,
    Android        String,
    ThirdToken     String,
    BundleName     String,
    Name           String,
    Avatar         UInt32,
    AvatarUrl      String,
    RegisterTime   UInt64
)
    engine = MergeTree PARTITION BY NowDate
        ORDER BY PlayerId
        SETTINGS index_granularity = 8192;

CREATE
MATERIALIZED VIEW r_register_view TO r_register_ods AS
SELECT *
FROM r_register_kafka;
