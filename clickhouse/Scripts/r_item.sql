create table r_item_kafka
(
    K<PERSON>         String,
    ProductID      UInt8,
    ChannelType    UInt32,
    Platform       UInt8,
    AppLanguage    UInt8,
    Country        String,
    AccType        UInt8,
    AppVersion     String,
    NowDate        Date,
    TimeStampValue DateTime,
    PlayerId       UInt64,
    ClaimId        String,
    InstanceId     Nullable(String),
    ItemId         Int64,
    ItemType       Int32,
    OptType        Int32,
    SrcType        Int32,
    CurCount       Int64,
    DeltaCount     Int64,
    OriginalData   String
)
    engine = Kafka SETTINGS kafka_broker_list = '************:9092', kafka_topic_list = 'r_item', kafka_group_name = 'ck', kafka_format = 'CSV', format_csv_delimiter = '|', kafka_skip_broken_messages = 10;

-- auto-generated definition
create table r_item_ods
(
    Ktable         String,
    ProductID      UInt8,
    ChannelType    UInt32,
    Platform       UInt8,
    AppLanguage    UInt8,
    Country        String,
    AccType        UInt8,
    AppVersion     String,
    NowDate        Date,
    TimeStampValue DateTime,
    PlayerId       UInt64,
    ClaimId        String,
    InstanceId     Nullable(String) default NULL,
    ItemId         Int64,
    ItemType       Int32,
    OptType        Int32,
    SrcType        Int32,
    CurCount       Int64,
    DeltaCount     Int64,
    OriginalData   String
)
    engine = MergeTree PARTITION BY NowDate
        ORDER BY PlayerId
        SETTINGS index_granularity = 8192;


CREATE
MATERIALIZED VIEW r_item_view TO r_item_ods AS
SELECT *
FROM r_item_kafka;
