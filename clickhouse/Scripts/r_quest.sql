create table r_quest_kafka
(
    K<PERSON>          String,
    ProductID       UInt8,
    ChannelType     UInt32,
    Platform        UInt8,
    AppLanguage     UInt8,
    Country         String,
    AccType         UInt8,
    AppVersion      String,
    NowDate         Date,
    TimeStampValue  DateTime,
    PlayerId        UInt64,
    QuestId         Int64,
    QuestMode       Int64,
    SelectedFishIds String,
    QuestCond       String,
    QuestReward     String

) engine = Kafka SETTINGS kafka_broker_list = '************:9092', kafka_topic_list = 'r_quest', kafka_group_name = 'ck', kafka_format = 'CSV', format_csv_delimiter = '|', kafka_skip_broken_messages = 10;

-- auto-generated definition
create table r_quest_ods
(
    Ktable          String,
    ProductID       UInt8,
    ChannelType     UInt32,
    Platform        UInt8,
    AppLanguage     UInt8,
    Country         String,
    AccType         UInt8,
    AppVersion      String,
    NowDate         Date,
    TimeStampValue  DateTime,
    PlayerId        UInt64,
    QuestId         Int64,
    QuestMode       Int64,
    SelectedFishIds String,
    QuestCond       String,
    QuestReward     String
) engine = MergeTree PARTITION BY NowDate
        ORDER BY PlayerId
        SETTINGS index_granularity = 8192;


CREATE
MATERIALIZED VIEW r_quest_view TO r_quest_ods AS
SELECT *
FROM r_quest_kafka;
