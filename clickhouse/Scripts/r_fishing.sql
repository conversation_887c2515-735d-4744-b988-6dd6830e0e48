create table r_fishing_kafka
(
    Ktable                  String,
    ProductID               UInt8,
    ChannelType             UInt32,
    Platform                UInt8,
    AppLanguage             UInt8,
    Country                 String,
    AccType                 UInt8,
    AppVersion              String,
    NowDate                 Date,
    TimeStampValue          DateTime,
    PlayerId                UInt64,
    PlayerLvl               Int32,
    Rod                     Int64,
    PlayerPos               String,
    Pond                    Int64,
    Bait                    Int64,
    Weather                 Int64,
    BaitPosEnterHitFish     String,
    WaterStructEnterHitFish String,
    WaterLayerEnterHitFish  String,
    WaterTempEnterHitFish   Int64,
    PoseList                String,
    FishQuality             Int64,
    BaitPosHitFish          String,
    WaterStructHitFish      String,
    WaterLayerHitFish       String,
    WaterTempHitFish        Int64,
    Length                  Int64,
    Weight                  Int64,
    BaitPosDrawRod          String,
    TimeElapsedHitFish      Int64,
    FishResult              Int32
)
    engine = Kafka SETTINGS kafka_broker_list = '************:9092', kafka_topic_list = 'r_fishing', kafka_group_name = 'ck', kafka_format = 'CSV', format_csv_delimiter = '|', kafka_skip_broken_messages = 10;


-- auto-generated definition
-- 钓鱼记录数据表
create table r_fishing_ods
(
    -- 标准元数据字段
    Ktable                  String,   -- 表标识
    ProductID               UInt8,    -- 产品ID
    ChannelType             UInt32,   -- 渠道类型
    Platform                UInt8,    -- 平台类型
    AppLanguage             UInt8,    -- 应用语言
    Country                 String,   -- 国家
    AccType                 UInt8,    -- 账户类型
    AppVersion              String,   -- 应用版本
    NowDate                 Date,     -- 日期
    TimeStampValue          DateTime, -- 时间戳

    -- 玩家基础信息
    PlayerId                UInt64,   -- 玩家ID
    PlayerLvl               Int32,    -- 玩家等级

    -- 基础抛竿信息
    Rod                     Int64,   -- 本次抛竿的竿itemid
    PlayerPos               String,   -- 抛竿时的玩家坐标（Vector3字符串，小数点后一位）x,y,z
    Pond                    Int64,   -- 钓场id
    Bait                    Int64,   -- 饵id（不分真饵/拟饵）
    Weather                 Int64,   -- 天气id

    -- 进入中鱼阶段时的状态
    BaitPosEnterHitFish     String,   -- 进入中鱼阶段的饵坐标
    WaterStructEnterHitFish String,   -- 进入中鱼阶段的水文结构id
    WaterLayerEnterHitFish  String,   -- 进入中鱼阶段的水层id
    WaterTempEnterHitFish   Int64,   -- 进入中鱼阶段的水温

    -- 饵姿态记录
    PoseList                String,   -- 饵姿态poseId列表（按时间顺序）

    -- 中鱼结果相关
    FishQuality             Int64,   -- 带品质的鱼种id（是否中鱼,中了不为0）
    BaitPosHitFish          String,   -- 中鱼时的饵坐标（仅中鱼有效）
    WaterStructHitFish      String,   -- 中鱼时的水文结构（仅中鱼有效）
    WaterLayerHitFish       String,   -- 中鱼时的水层（仅中鱼有效）
    WaterTempHitFish        Int64,   -- 中鱼时的水温（仅中鱼有效）
    Length                  Int64,   -- 鱼长度（厘米）
    Weight                  Int64,   -- 鱼重量（克）

    -- 收竿信息
    BaitPosDrawRod          String,   -- 收竿时的饵坐标
    TimeElapsedHitFish      Int64,   -- 中鱼阶段持续时间（毫秒）
    FishResult              Int64     -- 中鱼结果
)
    engine = MergeTree PARTITION BY NowDate -- 按日期分区，便于数据管理和查询优化
        ORDER BY PlayerId -- 按玩家ID排序，便于查询特定玩家记录
        SETTINGS index_granularity = 8192; -- 索引粒度设置

CREATE MATERIALIZED VIEW r_fishing_view TO r_fishing_ods AS
SELECT *
FROM r_fishing_kafka;
