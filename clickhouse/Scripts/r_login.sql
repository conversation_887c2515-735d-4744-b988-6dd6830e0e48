-- auto-generated definition
create table r_login_kafka
(
    Ktable         String,
    ProductID      UInt8,
    ChannelType    UInt32,
    Platform       UInt8,
    AppLanguage    UInt8,
    Country        String,
    AccType        UInt8,
    AppVersion     String,
    NowDate        Date,
    TimeStampValue DateTime,
    PlayerId       UInt64,
    LoginType      UInt8,
    LastLoginTime  UInt64,
    DeviceModel    String,
    DeviceBrand    String,
    Os             String,
    OsLanguage     String,
    Resolution     String,
    IP             String
)
    engine = Kafka SETTINGS kafka_broker_list = '************:9092', kafka_topic_list = 'r_login', kafka_group_name = 'ck', kafka_format = 'CSV', format_csv_delimiter = '|', kafka_skip_broken_messages = 10;

-- auto-generated definition
create table r_login_ods
(
    Ktable         String,
    ProductID      UInt8,
    ChannelType    UInt32,
    Platform       UInt8,
    AppLanguage    UInt8,
    Country        String,
    AccType        UInt8,
    AppVersion     String,
    NowDate        Date,
    TimeStampValue DateTime,
    PlayerId       UInt64,
    LoginType      UInt8,
    LastLoginTime  UInt64,
    DeviceModel    String,
    DeviceBrand    String,
    Os             String,
    OsLanguage     String,
    Resolution     String,
    IP             String
)
    engine = MergeTree PARTITION BY NowDate
        ORDER BY PlayerId
        SETTINGS index_granularity = 8192;

CREATE MATERIALIZED VIEW r_login_view TO r_login_ods AS
SELECT *
FROM r_login_kafka;
