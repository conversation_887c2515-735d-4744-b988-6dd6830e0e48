create table r_novice_scene_kafka
(
    Ktable         String,
    ProductID      UInt8,
    ChannelType    UInt32,
    Platform       UInt8,
    AppLanguage    UInt8,
    Country        String,
    AccType        UInt8,
    AppVersion     String,
    NowDate        Date,
    TimeStampValue DateTime,
    PlayerId       UInt64,
    GuideId        UInt32,
    GuideDuration  UInt64
) engine = Kafka SETTINGS kafka_broker_list = '************:9092', kafka_topic_list = 'r_novice_scene', kafka_group_name = 'ck', kafka_format = 'CSV', format_csv_delimiter = '|', kafka_skip_broken_messages = 10;

create table r_novice_scene_ods
(
    Ktable         String,
    ProductID      UInt8,
    ChannelType    UInt32,
    Platform       UInt8,
    AppLanguage    UInt8,
    Country        String,
    AccType        UInt8,
    AppVersion     String,
    NowDate        Date,
    TimeStampValue DateTime,
    PlayerId       UInt64,
    GuideId        UInt32,
    GuideDuration  UInt64
) engine = MergeTree PARTITION BY NowDate
        ORDER BY PlayerId
        SETTINGS index_granularity = 8192;

CREATE
MATERIALIZED VIEW r_novice_scene_view TO r_novice_scene_ods AS
SELECT *
FROM r_novice_scene_kafka;
