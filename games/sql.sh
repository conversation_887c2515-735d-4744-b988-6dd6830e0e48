#!/bin/bash

SQL_USER=root
SQL_PASSWD=fancydb2024#
# HOST_NAME=************
HOST_PORT=3306
table_path=table

# 用于过滤 -p Warning信息
export MYSQL_PWD=$SQL_PASSWD

function execute_sql()
{
    if [ -z "$1" ]; then
        echo "请提供SQL目录路径"
        exit 1
    fi

    sql_path=$1
    if [ -z "$2" ]; then
        # 如果没有提供文件列表，则执行目录下所有SQL文件
        list=$(ls "$table_path/$sql_path"/*.sql 2>/dev/null)
    else
        list=$2
    fi

    if [ -z "$list" ]; then
        echo "在 $table_path/$sql_path 目录下没有找到SQL文件"
        exit 1
    fi

    # 执行文件
    for file in $list; do
        if [ -f "$file" ]; then
            echo "开始执行: $file"
            mysql -u $SQL_USER -h $HOST_NAME --port $HOST_PORT $sql_path < "$file"
            if [ $? -eq 0 ]; then
                echo "执行成功: $file"
            else
                echo "执行失败: $file"
            fi
        fi
    done
}

# 检查参数
if [ $# -lt 1 ]; then
    echo "使用方法: $0 <sql目录> [sql文件列表]"
    exit 1
fi

execute_sql "$1" "$2"
