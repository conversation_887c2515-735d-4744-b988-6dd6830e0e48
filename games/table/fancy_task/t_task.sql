-- fancy_task.t_task definition

CREATE TABLE if not exists `t_task` (
  `player_id` bigint(20) NOT NULL COMMENT '玩家ID',
  `task_id` bigint(20) NOT NULL COMMENT '任务ID',
  `category` int(11) DEFAULT NULL COMMENT '任务类型',
  `sub_type` bigint(20) DEFAULT NULL COMMENT '子任务类型',
  `cond_list` text COMMENT '条件列表',
  `status` int(11) DEFAULT NULL COMMENT '任务状态',
  `number` int(11) DEFAULT NULL COMMENT '完成次数',
  `start_ts` bigint(20) DEFAULT NULL COMMENT '开始时间',
  `end_ts` bigint(20) DEFAULT NULL COMMENT '结束时间',
  `update_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`player_id`,`task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;