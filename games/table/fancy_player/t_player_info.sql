-- fancy_player.t_player_info definition

CREATE TABLE if not exists `t_player_info` (
  `player_id` bigint(20) NOT NULL,
  `product_id` int(11) DEFAULT NULL,
  `channel_id` int(11) DEFAULT NULL,
  `show_id` varchar(255) DEFAULT NULL,
  `nick_name` varchar(255) DEFAULT NULL,
  `real_name` varchar(255) DEFAULT NULL,
  `third_name` varchar(255) DEFAULT NULL,
  `country` varchar(255) DEFAULT NULL,
  `language_type` varchar(255) DEFAULT NULL,
  `id_card` varchar(255) DEFAULT NULL,
  `sex` int(11) DEFAULT NULL,
  `avatar` int(11) DEFAULT NULL,
  `frame` int(11) DEFAULT NULL,
  `lev` int(11) DEFAULT NULL,
  `novice` tinyint(1) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `last_login_time` datetime DEFAULT NULL,
  `offline_time` datetime DEFAULT NULL,
  `last_login_device_code` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`player_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;