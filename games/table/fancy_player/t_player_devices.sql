-- fancy_player.t_player_devices definition

CREATE TABLE if not exists `t_player_devices` (
  `player_id` bigint NOT NULL,
  `product_id` int DEFAULT NULL,
  `bundle_name` varchar(255) DEFAULT NULL,
  `device_name` varchar(255) DEFAULT NULL,
  `device_brand` varchar(255) DEFAULT NULL,
  `device_model` varchar(255) DEFAULT NULL,
  `mvno` varchar(255) DEFAULT NULL,
  `android_id` varchar(255) DEFAULT NULL,
  `os_language` varchar(255) DEFAULT NULL,
  `mac_addr` varchar(255) DEFAULT NULL,
  `client_ip` varchar(255) DEFAULT NULL,
  `network_type` int DEFAULT NULL,
  `os` varchar(255) DEFAULT NULL,
  `os_version` varchar(255) DEFAULT NULL,
  `firebase_token` varchar(255) DEFAULT NULL,
  `idfa` varchar(255) DEFAULT NULL,
  `resolution` varchar(255) DEFAULT NULL,
  `cpu` varchar(255) DEFAULT NULL,
  `directx` varchar(255) DEFAULT NULL,
  `ram` varchar(255) DEFAULT NULL,
  `video_adapter` varchar(255) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`player_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;