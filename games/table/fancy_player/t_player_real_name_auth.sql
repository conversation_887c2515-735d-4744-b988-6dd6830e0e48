-- fancy_player.t_player_real_name_auth definition

CREATE TABLE if not exists `t_player_real_name_auth` (
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `player_id` bigint(20) NOT NULL COMMENT '玩家ID',
  `pi` varchar(255) NOT NULL DEFAULT '' COMMENT '实名认证返回pi',
  `real_name` varchar(255) NOT NULL DEFAULT '' COMMENT '真实姓名',
  `id_card_num` varchar(255) NOT NULL DEFAULT '' COMMENT '身份证号码',
  `year` int(11) NOT NULL DEFAULT '0' COMMENT '年',
  `month` int(11) NOT NULL DEFAULT '0' COMMENT '月',
  `day` int(11) NOT NULL DEFAULT '0' COMMENT '日',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`product_id`,`player_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

