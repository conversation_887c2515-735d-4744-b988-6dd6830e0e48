-- fancy_player.t_player_master definition

CREATE TABLE if not exists `t_player_master` (
  `player_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增',
  `account` varchar(128) DEFAULT NULL,
  `password` varchar(128) DEFAULT NULL,
  `product_id` int(11) DEFAULT NULL,
  `channel_id` int(11) DEFAULT NULL,
  `platform` int(11) DEFAULT NULL,
  `status` int(11) DEFAULT NULL,
  `migrate_from` varchar(255) DEFAULT NULL,
  `acc_type` int(11) DEFAULT NULL,
  `last_login_type` int(11) DEFAULT NULL,
  `login_type` int(11) DEFAULT NULL,
  `adjust_id` varchar(255) DEFAULT NULL,
  `wechat_app_id` varchar(255) DEFAULT NULL,
  `wechat_open_id` varchar(255) DEFAULT NULL,
  `wechat_union_id` varchar(255) DEFAULT NULL,
  `wechat_session_key` varchar(255) DEFAULT NULL,
  `device_code` varchar(255) DEFAULT NULL,
  `open_id` varchar(255) DEFAULT NULL,
  `apple_id` varchar(255) DEFAULT NULL,
  `apple_token` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `last_login_time` timestamp NULL DEFAULT NULL,
  `offline_time` timestamp NULL DEFAULT NULL,
  `client_version` varchar(255) DEFAULT NULL,
  `app_language` int(11) DEFAULT NULL,
  `ban_reason` int(11) DEFAULT NULL,
  `ban_time` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_login_device_code` varchar(255) DEFAULT NULL,
  `real_name_auth` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`player_id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4;