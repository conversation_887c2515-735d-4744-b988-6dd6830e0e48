-- fancy_player.t_player_extend definition

CREATE TABLE if not exists `t_player_extend` (
  `player_id` bigint(20) NOT NULL,
  `product_id` int(11) DEFAULT NULL,
  `novice_guide` int(11) DEFAULT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`player_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='玩家扩展表';

