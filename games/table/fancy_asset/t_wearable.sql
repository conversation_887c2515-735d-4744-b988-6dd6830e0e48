-- fancy_asset.t_wearable definition

CREATE TABLE if not exists `t_wearable` (
  `product_id` int NOT NULL COMMENT '产品ID',
  `player_id` bigint NOT NULL COMMENT '玩家ID',
  `item_type` bigint NOT NULL COMMENT '物品类型',
  `item_id` bigint NOT NULL COMMENT '道具ID',
  `expire_time` bigint NOT NULL COMMENT '过期时间',
  `create_by` varchar(64) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_by` varchar(64) NOT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`product_id`,`player_id`,`item_type`,`item_id`),
  KEY `IDX_t_wearable_itemId` (`product_id`,`player_id`,`item_type`,`item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;