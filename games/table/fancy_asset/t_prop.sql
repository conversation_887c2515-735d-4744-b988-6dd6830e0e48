-- fancy_asset.t_prop definition

CREATE TABLE if not exists `t_prop` (
  `product_id` int NOT NULL COMMENT '产品ID',
  `player_id` bigint NOT NULL COMMENT '玩家ID',
  `instance_id` varchar(36) NOT NULL COMMENT '实例ID',
  `storage` int NOT NULL COMMENT '存储类型',
  `item_id` bigint NOT NULL COMMENT '道具ID',
  `category` bigint NOT NULL COMMENT '类别',
  `item_type` bigint NOT NULL COMMENT '物品类型',
  `amount` bigint NOT NULL COMMENT '数量',
  `expire_time` bigint NOT NULL COMMENT '过期时间',
  `args` varchar(128) DEFAULT NULL COMMENT '参数',
  `create_by` varchar(64) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_by` varchar(64) NOT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`product_id`,`player_id`,`instance_id`,`storage`),
  KEY `IDX_t_prop_category` (`product_id`,`player_id`,`item_id`,`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;