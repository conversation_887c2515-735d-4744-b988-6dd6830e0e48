-- auto-generated definition
create table t_ann_popup
(
    id         bigint auto_increment comment '唯一标识'
        primary key,
    priority   int                  not null comment '优先级（数值越小越优先）',
    channel_id int                  not null comment '渠道id',
    content    text                 not null comment '内容（JSON 格式）',
    pop_style  int        default 1 not null,
    action     text                 not null comment '动作（JSON 格式）',
    conditions text                 not null comment '条件（JSON 格式）',
    enable     tinyint(1) default 1 null comment '是否启用',
    start_time bigint               not null comment '生效开始时间（秒级 Unix 时间戳）',
    end_time   bigint               not null comment '生效结束时间（秒级 Unix 时间戳）',
    created_at datetime             not null,
    updated_at datetime             not null
)
    comment '拍脸图配置表';

create index idx_t_ann_popup_effective_time
    on t_ann_popup (start_time, end_time);

create index idx_t_ann_popup_enable
    on t_ann_popup (channel_id, enable);

