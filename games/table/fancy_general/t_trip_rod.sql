-- fancy_general.t_trip_rod definition

CREATE TABLE if not exists `t_trip_rod` (
  `player_id` bigint NOT NULL COMMENT '用户id',
  `id` bigint NOT NULL COMMENT '钓组id',
  `sit_id` int NOT NULL COMMENT '装备位置id',
  `item_id` bigint DEFAULT NULL COMMENT '道具id',
  `instance_id` varchar(255) DEFAULT NULL COMMENT '实例id',
  `update_at` bigint DEFAULT NULL,
  PRIMARY KEY (`player_id`,`id`,`sit_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 2025/1/6 道具属性添加
ALTER TABLE fancy_general.t_trip_rod ADD args varchar(128) NULL;