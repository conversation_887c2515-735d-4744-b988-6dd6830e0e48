-- auto-generated definition
create table t_cdk_record
(
    id         bigint unsigned auto_increment comment '自增主键 ID'
        primary key,
    batch_id   bigint unsigned                     not null comment '批次ID (关联 t_cdk_batch.id)',
    cdk        varchar(64)                         not null comment 'CDK码 ',
    used_bm    text                                null comment '兑换账号bitmap ',
    used_count int       default 0                 not null comment '已使用次数',
    created_at timestamp default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    constraint uniq_cdk
        unique (cdk) comment 'CDK码唯一索引，确保CDK不重复'
)
    comment 'CDK码记录表' collate = utf8mb4_unicode_ci;

create index idx_batch_id
    on t_cdk_record (batch_id)
    comment '批次ID索引，便于查询同一批次下的所有CDK';

