-- fancy_general.t_personal_mail definition

CREATE TABLE if not exists`t_personal_mail` (
  `mail_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '邮件id(主键)',
  `product_id` int(10) NOT NULL COMMENT '产品ID',
  `player_id` bigint(20) NOT NULL COMMENT '玩家ID',
  `mail_type` tinyint(4) DEFAULT '0' COMMENT '邮件类型: 1=普通',
  `title` text COMMENT '邮件标题',
  `content` text COMMENT '邮件内容',
  `attach` varchar(256) DEFAULT NULL COMMENT '邮件附件：json格式',
  `extend` text COMMENT '扩展数据',
  `attach_flg` tinyint(1) DEFAULT NULL COMMENT '是否领取附件: 0=未领',
  `read` tinyint(1) DEFAULT '0' COMMENT '已读: 0=未读',
  `icon` text COMMENT '缩略图',
  `view_type` int(11) DEFAULT '0' COMMENT '视图类型',
  `sender` varchar(100) DEFAULT NULL COMMENT '发送者',
  `expire_time` datetime NOT NULL COMMENT '到期时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  `create_by` varchar(64) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_by` varchar(64) NOT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`mail_id`),
  UNIQUE KEY `mail_id` (`mail_id`),
  KEY `product_id` (`product_id`),
  KEY `player_id` (`player_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

alter table t_personal_mail add column `template_id` int(11) DEFAULT '0' COMMENT '模板ID';
ALTER TABLE fancy_general.t_personal_mail MODIFY COLUMN attach text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '邮件附件：json格式';
