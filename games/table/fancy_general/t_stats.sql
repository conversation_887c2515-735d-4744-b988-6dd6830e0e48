-- fancy_general.t_stats definition

CREATE TABLE if not exists `t_stats` (
  `player_id` bigint(20) NOT NULL COMMENT '玩家id',
  `product_id` int(11) NOT NULL COMMENT '产品id',
  `cond_key` bigint(20) NOT NULL COMMENT 'key',
  `add_type` int(11) DEFAULT NULL COMMENT '增加类型',
  `val` bigint(20) DEFAULT NULL COMMENT '数值',
  `extra` varchar(255) DEFAULT NULL COMMENT '拓展信息',
  `update_at` bigint(20) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`player_id`,`product_id`,`cond_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;