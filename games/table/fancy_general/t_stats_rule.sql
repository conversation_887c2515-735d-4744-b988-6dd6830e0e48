-- fancy_general.t_stats_rule definition

CREATE TABLE `t_stats_rule` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `player_id` bigint(20) DEFAULT NULL COMMENT '玩家id',
  `typ` int(11) DEFAULT NULL COMMENT '类型',
  `target` int(11) DEFAULT NULL COMMENT '目标',
  `field` int(11) DEFAULT NULL COMMENT '字段',
  `add_type` int(11) DEFAULT NULL COMMENT '增加类型',
  `val` bigint(20) DEFAULT NULL COMMENT '数值',
  `extra` text COMMENT '拓展信息',
  `update_at` bigint(20) DEFAULT NULL COMMENT '更新时间',
  `cond_key` int(11) DEFAULT '-1' COMMENT '条件key',
  `cond_val` bigint(20) DEFAULT '0' COMMENT '条件值',
  PRIMARY KEY (`id`),
  KEY `IDX_t_stats_rule_index_player` (`player_id`,`typ`,`target`,`field`,`add_type`)
) ENGINE=InnoDB ;