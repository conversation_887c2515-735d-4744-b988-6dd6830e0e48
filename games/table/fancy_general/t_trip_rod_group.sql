-- fancy_general.t_trip_rod_group definition

create table if not exists t_trip_rod_group
(
    id        bigint primary key auto_increment comment '钓组id',
    player_id bigint            not null comment '用户id',
    name      varchar(255)      not null comment '钓组名称',
    update_at bigint            not null comment '更新时间戳',
    bag_index tinyint default 0 not null comment '背包位置'
);

create index t_trip_rod_group_player_id_bag_index_index
    on t_trip_rod_group (player_id, bag_index);

ALTER TABLE fancy_general.t_trip_rod_group ADD COLUMN `check` int(11) NOT NULL DEFAULT '0' COMMENT '检查状态';