-- fancy_general.t_player_state definition

CREATE TABLE if not exists `t_player_state`
(
    `player_id`   bigint(20) NOT NULL COMMENT '玩家ID',
    `goods_buy` text COMMENT '限制单次购买商品 bitmap',
    `exp_lv`      varchar(255) NOT NULL DEFAULT '' COMMENT '经验等级奖励 bitmap',
    `update_time` timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`player_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

alter table t_player_state
    add red_dot text default null null comment '红点bitmap' after exp_lv;
