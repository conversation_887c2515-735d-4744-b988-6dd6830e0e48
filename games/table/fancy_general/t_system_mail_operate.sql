-- fancy_general.t_system_mail_operate definition

CREATE TABLE if not exists `t_system_mail_operate` (
  `product_id` int(10) NOT NULL COMMENT '产品ID',
  `player_id` bigint(20) NOT NULL COMMENT '玩家ID',
  `read_mails` text COMMENT '已读表',
  `claim_mails` text COMMENT '已领取表',
  `update_by` varchar(64) NOT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`product_id`,`player_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;