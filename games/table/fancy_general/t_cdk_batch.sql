-- auto-generated definition
create table t_cdk_batch
(
    id                bigint unsigned auto_increment comment '自增主键 ID'
        primary key,
    channel_id        int          default 0                 not null comment '渠道ID',
    description       varchar(255) default ''                not null comment '批次描述',
    generation_option tinyint      default 0                 not null comment 'CDK生成方式 (1: 随机生成, 2: 手动输入)',
    start_time        timestamp    default CURRENT_TIMESTAMP not null comment '生效开始时间',
    end_time          timestamp    default CURRENT_TIMESTAMP not null comment '生效结束时间',
    cdk_count         int          default 0                 not null comment 'CDK数量 (此批次生成的CDK总数)',
    cdk_limit         int          default 0                 not null comment '单个CDK可被所有用户使用的总次数 (0 表示不限制, -1 也常用于表示不限制，具体根据业务逻辑统一)',
    rewards           json                                   not null comment '奖励内容 (JSON格式: [{"item_id": 1, "count": 5}])',
    status            tinyint      default 1                 not null comment '状态 (1=有效, 2=已作废)',
    created_at        timestamp    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at        timestamp    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment 'CDK批次表' collate = utf8mb4_unicode_ci;

create index idx_channel_id
    on t_cdk_batch (channel_id)
    comment '渠道ID索引，便于按渠道查询';

create index idx_start_time_end_time
    on t_cdk_batch (start_time, end_time)
    comment '时间范围索引，便于查询有效期内的批次';

create index idx_status
    on t_cdk_batch (status)
    comment '状态索引，便于查询特定状态的批次';

