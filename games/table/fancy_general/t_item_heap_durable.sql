-- fancy_general.t_item_heap_durable definition
-- 堆道具耐久表

CREATE TABLE if not exists`t_item_heap_durable` (
  `player_id`   bigint(20) NOT NULL COMMENT '玩家ID',
  `item_id`     bigint(20) NOT NULL COMMENT '道具ID',
  `durable_per` int NOT NULL COMMENT '耐久度百分比',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`player_id`,`item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;