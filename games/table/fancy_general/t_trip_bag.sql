-- fancy_general.t_trip_bag definition

CREATE TABLE if not exists `t_trip_bag` (
  `player_id` bigint NOT NULL COMMENT '用户id',
  `bag_type` int NOT NULL COMMENT '背包类型',
  `item_id` bigint NOT NULL COMMENT '道具id',
  `instance_id` varchar(255) NOT NULL COMMENT '实例id',
  `count` bigint DEFAULT NULL COMMENT '数量',
  `create_at` bigint DEFAULT NULL COMMENT '创建时间',
  `update_at` bigint DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`player_id`,`bag_type`,`item_id`,`instance_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;